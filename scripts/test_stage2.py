#!/usr/bin/env python3
"""
阶段二测试脚本 - 验证批量处理框架和基本功能
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加脚本目录到Python路径
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

from run_cpp_analysis import CppKitManager, CppAnalysisProcessor
from core_modules import Config, console
from asset_package_manager import AssetPackageManager

def create_mock_cpp_result(result_dir: str, asset_name: str, kit_name: str) -> bool:
    """创建模拟的C++处理结果"""
    try:
        result_dir = Path(result_dir)
        
        # 创建模拟的JSON输出
        mock_result = {
            "asset_name": asset_name,
            "kit_name": kit_name,
            "processing_info": {
                "processed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
                "status": "success",
                "frame_count": 50,
                "detected_faces": 45,
                "eye_tracking_quality": 0.85,
                "distraction_events": 3
            },
            "analysis_results": [
                {
                    "frame_id": i,
                    "timestamp": i * 0.1,
                    "face_detected": True,
                    "eye_openness": 0.8 + (i % 10) * 0.02,
                    "gaze_direction": {"x": 0.1, "y": 0.05},
                    "distraction_score": 0.2 + (i % 5) * 0.1
                }
                for i in range(50)
            ]
        }
        
        # 保存模拟结果
        output_file = result_dir / "analysis_result.json"
        with open(output_file, 'w') as f:
            json.dump(mock_result, f, indent=2, ensure_ascii=False)
        
        # 创建处理日志
        log_file = result_dir / "cpp_output.log"
        with open(log_file, 'w') as f:
            f.write("=== 模拟C++程序执行日志 ===\n")
            f.write(f"资产包: {asset_name}\n")
            f.write(f"工具包: {kit_name}\n")
            f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("状态: 成功\n")
            f.write("检测到人脸: 45/50 帧\n")
            f.write("眼部跟踪质量: 85%\n")
            f.write("分心事件: 3次\n")
        
        return True
        
    except Exception as e:
        if console:
            console.print(f"[red]创建模拟结果失败: {e}[/red]")
        else:
            print(f"创建模拟结果失败: {e}")
        return False

class MockCppAnalysisProcessor(CppAnalysisProcessor):
    """模拟的C++分析处理器"""
    
    def call_cpp_program(self, kit_info, input_file, output_dir, timeout=300):
        """模拟C++程序调用"""
        try:
            if console:
                console.print(f"[blue]模拟执行C++程序: {kit_info['executable']}[/blue]")
            else:
                print(f"模拟执行C++程序: {kit_info['executable']}")
            
            # 模拟处理时间
            time.sleep(2)
            
            # 创建模拟结果
            asset_name = Path(output_dir).name.split('_')[0]
            kit_name = kit_info['metadata']['kit_info']['name']
            
            if create_mock_cpp_result(output_dir, asset_name, kit_name):
                return True, "模拟处理成功，耗时 2.0秒"
            else:
                return False, "模拟结果创建失败"
                
        except Exception as e:
            return False, f"模拟处理异常: {e}"

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试阶段二基本功能 ===")
    
    # 1. 测试C++工具包管理器
    print("\n1. 测试C++工具包管理器...")
    cpp_kit_manager = CppKitManager()
    kits = cpp_kit_manager.list_kits()
    print(f"   发现 {len(kits)} 个C++工具包: {kits}")
    
    if not kits:
        print("   ❌ 没有找到C++工具包")
        return False
    
    # 2. 测试资产包管理器
    print("\n2. 测试资产包管理器...")
    asset_manager = AssetPackageManager()
    assets = asset_manager.list_asset_packages()
    print(f"   发现 {len(assets)} 个资产包: {assets}")
    
    if not assets:
        print("   ❌ 没有找到资产包")
        return False
    
    # 3. 测试单个处理
    print("\n3. 测试单个资产包处理...")
    config = Config()
    processor = MockCppAnalysisProcessor(config, cpp_kit_manager)
    
    test_asset = assets[0]
    test_kit = kits[0]
    
    print(f"   处理资产包: {test_asset}")
    print(f"   使用工具包: {test_kit}")
    
    success, message = processor.process_asset_package(test_asset, test_kit)
    if success:
        print(f"   ✅ 处理成功: {message}")
    else:
        print(f"   ❌ 处理失败: {message}")
        return False
    
    return True

def test_batch_processing():
    """测试批量处理"""
    print("\n=== 测试批量处理功能 ===")
    
    # 初始化组件
    config = Config()
    cpp_kit_manager = CppKitManager()
    asset_manager = AssetPackageManager()
    processor = MockCppAnalysisProcessor(config, cpp_kit_manager)
    
    # 获取测试数据
    assets = asset_manager.list_asset_packages()
    kits = cpp_kit_manager.list_kits()
    
    if not assets or not kits:
        print("❌ 缺少测试数据")
        return False
    
    # 选择测试数据（最多2个资产包和1个工具包）
    test_assets = assets[:2]
    test_kits = kits[:1]
    
    print(f"批量处理: {len(test_assets)}个资产包 × {len(test_kits)}个工具包")
    print(f"资产包: {test_assets}")
    print(f"工具包: {test_kits}")
    
    # 执行批量处理
    start_time = time.time()
    summary = processor.batch_process(test_assets, test_kits, max_workers=2)
    total_time = time.time() - start_time
    
    # 显示结果
    print(f"\n批量处理完成:")
    print(f"  总任务数: {summary['total_tasks']}")
    print(f"  成功任务: {summary['successful_tasks']}")
    print(f"  失败任务: {summary['failed_tasks']}")
    print(f"  总耗时: {total_time:.2f}秒")
    print(f"  平均耗时: {summary['average_time_per_task']:.2f}秒/任务")
    
    # 验证结果
    if summary['failed_tasks'] == 0:
        print("✅ 批量处理全部成功")
        return True
    else:
        print("❌ 部分任务失败")
        for result in summary['results']:
            if not result['success']:
                print(f"   失败: {result['asset_name']} + {result['kit_name']}: {result['message']}")
        return False

def test_result_validation():
    """测试结果验证"""
    print("\n=== 测试结果验证 ===")
    
    # 查找最新的处理结果
    results_dir = Path("results")
    if not results_dir.exists():
        print("❌ 结果目录不存在")
        return False
    
    result_dirs = [d for d in results_dir.iterdir() if d.is_dir() and d.name.startswith(('test_small_001', 'haibaodms_test_001'))]
    
    if not result_dirs:
        print("❌ 没有找到处理结果")
        return False
    
    # 验证最新的结果
    latest_result = max(result_dirs, key=lambda x: x.stat().st_mtime)
    print(f"验证结果目录: {latest_result}")
    
    # 检查必需文件
    required_files = ["result_metadata.json", "analysis_result.json", "cpp_output.log"]
    missing_files = []
    
    for file_name in required_files:
        file_path = latest_result / file_name
        if file_path.exists():
            print(f"  ✅ {file_name} ({file_path.stat().st_size} bytes)")
        else:
            missing_files.append(file_name)
            print(f"  ❌ {file_name} (缺失)")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    # 验证JSON文件格式
    try:
        with open(latest_result / "result_metadata.json", 'r') as f:
            metadata = json.load(f)
        print(f"  ✅ result_metadata.json 格式正确")
        
        with open(latest_result / "analysis_result.json", 'r') as f:
            analysis = json.load(f)
        print(f"  ✅ analysis_result.json 格式正确，包含 {len(analysis.get('analysis_results', []))} 帧结果")
        
    except Exception as e:
        print(f"  ❌ JSON文件格式错误: {e}")
        return False
    
    print("✅ 结果验证通过")
    return True

def main():
    """主函数"""
    print("DMS自动化分析与渲染平台 - 阶段二测试")
    print("=" * 50)
    
    tests = [
        ("基本功能测试", test_basic_functionality),
        ("批量处理测试", test_batch_processing),
        ("结果验证测试", test_result_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！阶段二核心功能正常")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return 1

if __name__ == '__main__':
    sys.exit(main())
