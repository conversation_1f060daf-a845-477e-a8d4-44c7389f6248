# 关键点双维度分析工具

一个强大的关键点数据分析工具，能够从**连续时间序列**和**每一帧关键点空间分布**两个维度全面分析数据质量和运动特征。

## 功能特点

### 🔍 双维度分析

#### 1. 时间序列维度分析
- **轨迹统计分析**: 每个关键点的位置方差、速度、加速度统计
- **运动相关性分析**: 关键点之间的运动协调性和相关性
- **频域分析**: 运动频率特征，识别自然运动和异常振动

#### 2. 空间关键点维度分析  
- **空间分布分析**: 关键点的几何特征、散布度、凸包面积
- **空间一致性分析**: 帧间形状保持度和位置稳定性
- **关键点质量评估**: 分散程度、均匀性、紧凑性评估

#### 3. 综合集成分析
- **时空耦合分析**: 空间变化与时间的关联关系
- **异常检测**: 基于统计模型的多维异常识别
- **质量综合评估**: 结合时间和空间的整体质量评分

### 📊 丰富的可视化输出

- 时间序列轨迹图表
- 运动相关性热图  
- 频率分析直方图
- 空间分布变化曲线
- 异常检测散点图
- 综合评估仪表盘

### 📄 详细的分析报告

- 全面的统计摘要
- 数据质量评估
- 异常情况分析
- 参数调优建议

## 安装依赖

```bash
pip install numpy pandas matplotlib seaborn scipy
```

## 使用方法

### 1. 命令行使用

```bash
# 基本用法
python keypoint_dual_dimension_analyzer.py -i data.json -o ./output

# 指定帧率和数据格式
python keypoint_dual_dimension_analyzer.py -i data.csv -o ./results --fps 25 --format csv

# 只生成报告，不生成图表
python keypoint_dual_dimension_analyzer.py -i data.json -o ./output --no-viz
```

### 2. Python脚本使用

```python
from keypoint_dual_dimension_analyzer import KeypointDualDimensionAnalyzer

# 创建分析器
analyzer = KeypointDualDimensionAnalyzer(fps=30.0)

# 加载数据
analyzer.load_data_from_json("keypoints_data.json")
# 或
analyzer.load_data_from_csv("keypoints_data.csv")

# 执行分析
temporal_results = analyzer.temporal_analysis()
spatial_results = analyzer.spatial_analysis() 
integrated_results = analyzer.integrated_analysis()

# 生成报告和可视化
analyzer.generate_comprehensive_report("./output")
analyzer.create_visualizations("./output")
```

### 3. 运行示例

```bash
# 运行内置示例
python keypoint_analysis_example.py
```

## 数据格式

### JSON格式

```json
[
  {
    "frame_id": 0,
    "timestamp": 0.0,
    "keypoints": [
      [x1, y1],
      [x2, y2],
      ...
    ]
  },
  ...
]
```

或字典格式：

```json
{
  "0": {
    "timestamp": 0.0,
    "keypoints": [[x1, y1], [x2, y2], ...]
  },
  "1": {
    "timestamp": 0.033,
    "keypoints": [[x1, y1], [x2, y2], ...]
  }
}
```

### CSV格式

```csv
frame_id,timestamp,x0,y0,x1,y1,x2,y2,...
0,0.0,100.5,200.3,110.2,205.1,...
1,0.033,101.2,201.0,111.0,206.2,...
```

## 分析指标说明

### 时间序列指标

| 指标 | 说明 | 单位 |
|------|------|------|
| 位置方差 | 关键点位置的时间稳定性 | 像素² |
| 平均速度 | 关键点移动的平均速率 | 像素/帧 |
| 平均加速度 | 运动变化的剧烈程度 | 像素/帧² |
| 运动相关性 | 关键点间运动的协调性 | -1到1 |
| 主导频率 | 运动的主要频率成分 | Hz |

### 空间分析指标

| 指标 | 说明 | 单位 |
|------|------|------|
| 散布度 | 关键点相对中心的分散程度 | 像素 |
| 凸包面积 | 关键点围成的最小凸多边形面积 | 像素² |
| 形状一致性 | 帧间形状保持度 | 0-1 |
| 均匀性 | 关键点分布的均匀程度 | 0-1 |

### 综合分析指标

| 指标 | 说明 | 应用 |
|------|------|------|
| 空间速度 | 综合空间变化率 | 运动强度评估 |
| 异常分数 | 多维异常检测评分 | 数据质量监控 |
| 时空耦合度 | 时间和空间变化的关联性 | 运动自然度评估 |

## 应用场景

### 1. DMS (驾驶员监控系统)
- 面部关键点质量评估
- 眼部跟踪稳定性分析
- 异常行为检测

### 2. 动作捕捉系统
- 运动数据质量评估
- 关键点轨迹平滑度分析
- 异常帧检测

### 3. 计算机视觉研究
- 关键点检测算法评估
- 跟踪算法性能分析
- 数据增强效果验证

### 4. 质量监控
- 实时数据质量监控
- 算法性能评估
- 参数调优指导

## 输出文件说明

### 分析报告 (`keypoint_analysis_report_YYYYMMDD_HHMMSS.txt`)
- 数据基本信息
- 各维度分析摘要
- 异常检测结果
- 调优建议

### 可视化图表
- `temporal_analysis_YYYYMMDD_HHMMSS.png`: 时间序列分析图表
- `spatial_analysis_YYYYMMDD_HHMMSS.png`: 空间分析图表  
- `integrated_analysis_YYYYMMDD_HHMMSS.png`: 综合分析图表

## 参数说明

### 命令行参数

```bash
-i, --input       输入数据文件路径 (必需)
-o, --output      输出目录 (默认: ./)
--fps            视频帧率 (默认: 30.0)
--format         数据格式: json|csv (自动检测)
--no-viz         不生成可视化图表
```

### 分析器参数

```python
KeypointDualDimensionAnalyzer(
    fps=30.0,           # 视频帧率
)
```

## 性能优化建议

### 大数据量处理
- 建议分批处理超过1万帧的数据
- 可选择关闭可视化以提升速度
- 考虑使用多进程并行分析

### 内存使用
- 大数据集建议使用CSV格式以减少内存占用
- 可分段加载和分析数据

## 故障排除

### 常见问题

1. **数据加载失败**
   - 检查文件格式是否正确
   - 确认关键点数据结构完整

2. **可视化图表空白**
   - 检查中文字体安装
   - 确认数据中有有效的数值

3. **分析结果异常**
   - 检查时间戳是否连续
   - 确认关键点坐标范围合理

### 调试模式

在脚本中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展开发

### 添加自定义分析指标

```python
class CustomAnalyzer(KeypointDualDimensionAnalyzer):
    def custom_metric(self):
        # 实现自定义分析逻辑
        pass
```

### 支持新的数据格式

```python
def load_custom_format(self, file_path):
    # 实现自定义格式加载
    pass
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

MIT License

---

**作者**: AI Assistant  
**版本**: 1.0.0  
**更新日期**: 2024年 