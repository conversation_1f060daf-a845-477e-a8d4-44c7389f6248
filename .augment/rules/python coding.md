---
### **一、 布局与格式化 (Layout & Formatting)**

一致的格式是代码可读性的基石。**强烈推荐使用自动化格式化工具，如 `black` 和 `isort`**，这能从根本上解决所有关于格式的争论。

#### **1.1 自动化工具**

*   **代码格式化**: 使用 `black` 统一代码风格。
*   **导入排序**: 使用 `isort` 自动排序和分组 `import` 语句。
*   **静态检查**: 使用 `flake8` 或 `pylint` 检查代码风格和常见错误。

#### **1.2 导入 (Imports)**

*   **原则**: 导入语句应始终位于文件顶部，并按以下顺序分组，组间用空行隔开：
    1.  标准库 (`os`, `sys`)
    2.  第三方库 (`requests`, `pandas`)
    3.  本项目内的模块 (`from my_project.utils import ...`)
*   **理由**: 清晰的依赖关系图，便于管理和定位。`isort` 会自动处理。

#### **1.3 行长度与缩进**

*   **行长度**: 推荐每行不超过 **88 个字符**（`black` 的默认值），这在现代宽屏显示器上是可读性与实用性的良好平衡。
*   **缩进**: 必须使用 **4个空格** 进行缩进，严禁使用制表符 (Tab)。

#### **1.4 空行**

*   **顶级定义**: 顶级函数和类定义之间使用 **两个** 空行。
*   **方法定义**: 类内部的方法定义之间使用 **一个** 空行。
*   **逻辑分隔**: 在函数内部，使用 **一个** 空行来分隔逻辑上独立的代码块。
type: "manual"
---
### **二、 命名约定 (Naming Conventions)**

见名知意，保持一致。

| 元素 | 命名法 | 示例 |
| :--- | :--- | :--- |
| **模块/包** | `snake_case` (全小写) | `http_client.py` |
| **函数/方法/变量** | `snake_case` | `get_user_info`, `user_id` |
| **类/异常** | `UpperCamelCase` (大驼峰) | `class HttpRequest;`, `class UserNotFound(Exception):` |
| **常量** | `ALL_CAPS` (全大写) | `MAX_CONNECTIONS = 100` |
| **受保护成员** | 单下划线前缀 `_` | `self._connection` |
| **私有成员** | 双下划线前缀 `__` | `self.__task_queue` |

*   **函数命名**: 推荐使用 **动词或动词+名词** 结构，如 `send_request()`、`is_valid()`。

---

### **三、 编程实践 (Programming Practices)**

编写健壮、高效且富有表现力的代码。

#### **3.1 变量与类型**

*   **类型注解**: **强烈推荐**为所有函数和方法的签名添加类型注解 (Python 3.6+)。
*   **理由**: 提高代码的可读性和健壮性，为静态分析工具（如 `mypy`）和 IDE 提供支持，有助于在编码阶段发现潜在的类型错误。
*   **示例**:
    ```python
    from typing import List, Dict

    def process_users(user_ids: List[int]) -> Dict[int, str]:
        # ... 实现逻辑
        return {1: "Alice"}
    ```
*   **避免全局变量**: 优先通过函数参数传递状态，而不是依赖可变的全局变量，以降低耦合度和提高可测试性。

#### **3.2 函数与方法设计**

*   **单一职责**: 每个函数应只做一件事，并把它做好。函数体过长（如超过 50 行）通常是需要重构的信号。
*   **参数设计**:
    *   避免过多的参数（建议不超过 4-5 个）。如果参数过多，考虑使用 `dataclass` 或 `TypedDict` 将其封装成一个对象。
    *   善用关键字参数和默认值，提高函数调用的可读性和灵活性。
*   **异常处理**:
    *   **捕获特定异常**，而不是宽泛的 `except Exception:`。
    *   不要“默默地”吞掉异常 (`except: pass`)，除非你有非常明确的理由。如果需要忽略，请添加注释说明原因。

#### **3.3 类与对象设计**

*   **数据类**: 对于主要用于存储数据的类，优先使用 `dataclasses` (Python 3.7+)。它会自动为你生成 `__init__`, `__repr__`, `__eq__` 等方法。
    ```python
    from dataclasses import dataclass

    @dataclass
    class Point:
        x: float
        y: float
    ```
*   **组合优于继承**: 在需要复用代码时，优先考虑将功能作为一个组件（对象）包含进来，而不是通过继承来获得。这能使系统更灵活、耦合度更低。
*   **魔术方法**: 合理实现魔术方法（Dunder methods）如 `__str__` (用户友好输出) 和 `__repr__` (开发者调试输出)，让你的对象行为更符合 Python 的习惯。

---

### **四、 Pythonic 之道 (The Pythonic Way)**

利用 Python 语言的特性，写出更简洁、更高效、更易读的代码。

*   **推导式**: 优先使用列表/字典/集合推导式和生成器表达式，它们比显式 `for` 循环更简洁、通常也更快。
    ```python
    # 推荐
    squares = [x**2 for x in range(10)]
    # 不推荐
    squares = []
    for x in range(10):
        squares.append(x**2)
    ```
*   **上下文管理器 (`with` 语句)**: 对于文件、锁、数据库连接等需要显式打开和关闭的资源，必须使用 `with` 语句，确保资源被自动、安全地释放。
*   **F-strings**: 格式化字符串时，**首选 F-strings** (Python 3.6+)。它们速度更快，也更易读。
    ```python
    name = "Alice"
    age = 30
    print(f"User: {name}, Age: {age}")
    ```
*   **生成器 (`yield`)**: 当处理大量数据或数据流时，使用生成器逐步产出结果，可以极大地节省内存。

---

### **五、 文档与注释 (Documentation & Comments)**

让代码自己说话，让注释解释“为什么”。

*   **文档字符串 (Docstrings)**:
    *   为所有公共的模块、函数、类和方法编写文档字符串。
    *   推荐使用 **Google 风格**或 **NumPy 风格**，因为它们结构清晰，并被 Sphinx 等文档生成工具良好支持。
    *   Docstring 应简要描述其功能，并说明参数（Args）、返回值（Returns）和可能抛出的异常（Raises）。
*   **行内注释**: 只在必要时使用行内注释 (`#`)，用于解释复杂的算法、业务逻辑的背景或代码的“陷阱”。好的命名和结构可以减少对注释的依赖。

*   **Google 风格 Docstring 示例**:
    ```python
    def calculate_average(numbers: List[float]) -> float:
        """Calculates the average of a list of numbers.

        Args:
            numbers: A list of numbers. An empty list is handled gracefully.

        Returns:
            The average of the numbers, or 0.0 if the list is empty.
        
        Raises:
            TypeError: If the list contains non-numeric elements.
        """
        if not numbers:
            return 0.0
        return sum(numbers) / len(numbers)
    ```

---

### **六、 代码审查清单 (Code Review Checklist)**

1.  **自动化检查通过**: `black`, `isort`, `flake8`, `mypy` 是否全部通过？
2.  **可读性**: 命名是否清晰？逻辑是否易于理解？函数是否遵循单一职责？
3.  **正确性**: 是否覆盖了所有逻辑分支和边界情况？异常处理是否恰当？
4.  **资源管理**: `with` 语句是否被正确使用？是否存在潜在的资源泄漏？
5.  **性能**: 是否有不必要的循环或数据拷贝？数据结构的选择是否高效（如 `set` vs `list`）？
6.  **Pythonic**: 是否充分利用了 Python 的语言特性（如推导式、F-strings）？
7.  **文档**: 公共 API 是否有清晰的 Docstring？

---
