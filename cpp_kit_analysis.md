# C++工具包结构分析报告

## 概述

基于对现有`cpp_excuter`目录和`dms_postmortem_optimised.py`的分析，本报告详细分析了C++工具包的结构、依赖关系和调用机制，为阶段二的实现提供技术指导。

## 1. 现有C++工具包结构分析

### 1.1 cpp_excuter目录结构

```
cpp_excuter/
├── 可执行文件
│   ├── test_dms_internal_postmortem    # 主要的DMS分析程序 (15.9MB)
│   ├── test_dms_withtxt               # 带文本输出的DMS程序 (15.9MB)
│   ├── test_tx_dms                    # TX DMS程序 (9.5MB)
│   └── test_tx_dms_tmp                # 临时TX DMS程序 (15.9MB)
├── 动态链接库
│   └── libtx_dms.so                   # 核心DMS算法库
├── 配置文件
│   └── ip_port.json                   # 网络配置 {"ip":"************","port":1180}
├── 输出目录
│   ├── log/                           # 程序执行日志
│   ├── output_data/                   # 眼部曲线数据输出
│   └── output_json/                   # JSON格式分析结果
├── 测试用例
│   └── testcase/                      # 各种测试场景文件
└── 辅助脚本
    ├── cpp_excuter_with_log_saved.py # 带日志保存的执行器
    ├── analy_curve.py                 # 曲线分析脚本
    └── eye_curve_parser.py            # 眼部曲线解析器
```

### 1.2 可执行文件分析

#### 文件类型
- **格式**: ELF 64-bit LSB shared object
- **架构**: x86-64
- **系统**: GNU/Linux 3.2.0
- **特点**: 动态链接，包含调试信息

#### 依赖关系 (以test_tx_dms_tmp为例)
```
核心依赖:
- libtx_dms.so (主要算法库)
- libdl.so.2 (动态链接)
- libpthread.so.0 (多线程支持)
- libstdc++.so.6 (C++标准库)

网络依赖:
- libcurl.so.4 (HTTP客户端)
- libssl.so.1.1 (SSL/TLS)
- libcrypto.so.1.1 (加密)

系统依赖:
- libc.so.6 (C标准库)
- libm.so.6 (数学库)
- libz.so.1 (压缩库)
```

### 1.3 程序调用机制

#### 当前调用方式 (来自dms_postmortem_optimised.py)
```python
def call_cpp_program(txt_file, outfile_name, print_cpp_info=True, process_container=None):
    # 1. 程序路径配置
    cpp_program_name = "test_dms_internal_postmortem"
    cpp_program_path = os.path.join(CPP_PROGRAM_DIR, cpp_program_name)
    
    # 2. 环境变量设置
    env = os.environ.copy()
    program_dir = os.path.dirname(os.path.abspath(cpp_program_path))
    env['LD_LIBRARY_PATH'] = f"{program_dir}:{current_ld_path}"
    
    # 3. 进程启动
    process = subprocess.Popen(
        [cpp_program_path, txt_file],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=print_cpp_info,
        env=env
    )
    
    # 4. 输出处理
    with open(output_file, 'w') as f:
        for line in process.stdout:
            f.write(line)
```

## 2. 标准C++工具包设计

### 2.1 目录结构规范

基于分析结果，设计标准的C++工具包格式：

```
cpp_kits/
└── {version_name}/
    ├── bin/                           # 可执行文件目录
    │   ├── test_dms_internal_postmortem
    │   ├── test_tx_dms_tmp
    │   └── ...
    ├── lib/                           # 动态链接库目录
    │   ├── libtx_dms.so
    │   └── ...
    ├── config/                        # 配置文件目录
    │   ├── ip_port.json
    │   └── ...
    ├── models/                        # 模型文件目录 (如需要)
    │   └── ...
    └── metadata.json                  # 工具包元数据
```

### 2.2 metadata.json格式

```json
{
  "kit_info": {
    "name": "DMS_Analysis_Kit_v1.0",
    "version": "1.0.0",
    "created_at": "2025-07-10T17:30:00Z",
    "description": "DMS算法分析工具包v1.0"
  },
  "executables": [
    {
      "name": "test_dms_internal_postmortem",
      "path": "bin/test_dms_internal_postmortem",
      "description": "主要的DMS分析程序",
      "input_format": "txt",
      "output_format": "json"
    }
  ],
  "libraries": [
    {
      "name": "libtx_dms.so",
      "path": "lib/libtx_dms.so",
      "description": "核心DMS算法库"
    }
  ],
  "config_files": [
    {
      "name": "ip_port.json",
      "path": "config/ip_port.json",
      "description": "网络配置文件"
    }
  ],
  "system_requirements": {
    "os": "Linux",
    "arch": "x86_64",
    "min_memory_mb": 1024,
    "dependencies": [
      "libcurl.so.4",
      "libssl.so.1.1",
      "libcrypto.so.1.1"
    ]
  },
  "validation": {
    "checksum_type": "md5",
    "checksums": {
      "bin/test_dms_internal_postmortem": "abc123...",
      "lib/libtx_dms.so": "def456..."
    }
  }
}
```

## 3. 环境配置和验证机制

### 3.1 环境配置需求

#### 系统环境
- **操作系统**: Linux x86_64
- **内存要求**: 最少1GB可用内存
- **磁盘空间**: 最少500MB可用空间

#### 依赖库检查
```bash
# 检查必需的系统库
ldd {executable_path} | grep "not found"

# 检查LD_LIBRARY_PATH设置
echo $LD_LIBRARY_PATH
```

#### 网络配置
- **远程服务器**: 需要SSH免密登录配置
- **服务配置**: ip_port.json中的IP和端口设置
- **防火墙**: 确保相关端口开放

### 3.2 MD5校验机制

#### 校验目标
- 可执行文件完整性
- 动态链接库完整性
- 配置文件完整性
- 模型文件完整性 (如有)

#### 校验流程
```python
def verify_cpp_kit(kit_path):
    """验证C++工具包完整性"""
    metadata_path = os.path.join(kit_path, "metadata.json")
    
    # 1. 读取元数据
    with open(metadata_path, 'r') as f:
        metadata = json.load(f)
    
    # 2. 验证文件存在性
    for executable in metadata['executables']:
        file_path = os.path.join(kit_path, executable['path'])
        if not os.path.exists(file_path):
            return False, f"Missing executable: {executable['name']}"
    
    # 3. 验证MD5校验和
    checksums = metadata['validation']['checksums']
    for file_path, expected_md5 in checksums.items():
        full_path = os.path.join(kit_path, file_path)
        actual_md5 = calculate_md5(full_path)
        if actual_md5 != expected_md5:
            return False, f"MD5 mismatch: {file_path}"
    
    return True, "Verification passed"
```

## 4. 批量处理架构设计

### 4.1 处理流程

```
帧资产包 → [环境配置] → [C++批量处理] → [结果收集] → JSON结果
    ↓           ↓              ↓            ↓
  验证资产    检查工具包      并发执行      汇总输出
```

### 4.2 核心组件

#### setup_and_verify.sh
- **功能**: 环境配置和验证
- **职责**: 
  - 检查系统依赖
  - 验证C++工具包完整性
  - 配置环境变量
  - 测试远程连接

#### run_cpp_analysis.py
- **功能**: 批量处理编排
- **职责**:
  - 读取帧资产包
  - 调度C++程序执行
  - 监控处理进度
  - 收集和整理结果

### 4.3 配置管理

#### 全局配置 (config.json)
```json
{
  "cpp_kits": [
    {
      "name": "v1.0",
      "kit_path": "cpp_kits/v1.0",
      "executable": "test_dms_internal_postmortem",
      "description": "DMS分析工具包v1.0"
    }
  ],
  "remote_server": {
    "host": "************",
    "username": "dms_user",
    "remote_path": "/opt/dms/models",
    "service_name": "dms_service"
  },
  "processing": {
    "max_concurrent": 4,
    "timeout_seconds": 300,
    "retry_count": 3
  }
}
```

## 5. 实现优先级

### 高优先级
1. **C++工具包标准化**: 定义标准目录结构和元数据格式
2. **环境验证脚本**: 实现setup_and_verify.sh
3. **基础批量处理**: 实现run_cpp_analysis.py的核心功能

### 中优先级
1. **MD5校验机制**: 完整的文件完整性验证
2. **远程服务器操作**: SSH连接和文件传输
3. **断点续传支持**: 处理中断和恢复机制

### 低优先级
1. **性能优化**: 并发处理和资源管理
2. **监控和日志**: 详细的处理状态监控
3. **错误恢复**: 自动重试和错误处理

## 6. 风险和挑战

### 技术风险
- **依赖库兼容性**: 不同环境下的库版本差异
- **内存管理**: 大量帧数据的内存使用
- **并发控制**: 多进程执行的资源竞争

### 解决方案
- **环境标准化**: 使用Docker或虚拟环境
- **资源监控**: 实时监控内存和CPU使用
- **进程池管理**: 限制并发数量和资源分配

## 7. 下一步行动

1. **立即开始**: 创建标准C++工具包目录结构
2. **实现验证**: 开发setup_and_verify.sh脚本
3. **核心处理**: 实现run_cpp_analysis.py基础功能
4. **测试验证**: 使用阶段一生成的帧资产包进行测试
